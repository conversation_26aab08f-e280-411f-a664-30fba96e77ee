"""
Vinyl Mastering Validator 2.0 – finální MM:SS verze
Kompletní funk<PERSON>, kter<PERSON>:
- načítá .zip / .rar
- extrahuje tracklist z PDF
- vrací všechny časy ve formátu MM:SS
- zachovává plnou funkcionalitu
"""

import os
import sys
import json
import zipfile
import threading
import tkinter as tk
from pathlib import Path
from tempfile import TemporaryDirectory
from tkinter import filedialog, messagebox, ttk

import requests
import fitz  # PyMuPDF
from dotenv import load_dotenv

# --- OPTIONAL .RAR SUPPORT ---
try:
    import rarfile
    RAR_AVAILABLE = True
except ImportError:
    RAR_AVAILABLE = False


# --- HELPER: převod sekund -> MM:SS ---
def sec_to_mmss(seconds: float | None) -> str | None:
    """Převede sekundy na řetězec MM:SS (nebo vrátí None)."""
    if seconds is None:
        return None
    minutes = int(seconds // 60)
    rest_sec = int(seconds % 60)
    return f"{minutes:02d}:{rest_sec:02d}"


# --- CORE AI LOGIC ---
class TracklistProcessorAI:
    API_URL = "https://openrouter.ai/api/v1/chat/completions"
    MODEL_NAME = "google/gemini-2.5-flash"

    def __init__(self, api_key: str, update_status_callback=None):
        if not api_key:
            raise ValueError("Chybí API klíč pro OpenRouter. Zkontrolujte .env soubor.")
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/your-repo",
            "X-Title": "Vinyl Mastering Validator",
        }
        self.update_status = update_status_callback

    def _log_status(self, message):
        if self.update_status:
            self.update_status(message)

    def _extract_text_from_pdf(self, pdf_path: Path) -> str:
        self._log_status("Extrahuji text z PDF...")
        with fitz.open(pdf_path) as doc:
            full_text = "".join(page.get_text() for page in doc)
        if not full_text.strip():
            raise IOError("PDF je prázdný nebo neobsahuje textovou vrstvu.")
        return full_text

    def extract_tracklist_data(self, pdf_path: Path) -> dict:
        pdf_text = self._extract_text_from_pdf(pdf_path)

        prompt = f"""
Jsi expert na hudební mastering. Analyzuj následující text z tracklistu
a vytvoř z něj kompletní, strukturovaná data pro vinyl.

Text obsahuje sekce SIDE A a SIDE B. Vrať POUZE JSON v tomto formátu:

{{
  "SIDE_A": {{
    "tracks": [
      {{"track_number": 1, "title": "...", "start_time_sec": 0, "end_time_sec": 123}}
    ]
  }},
  "SIDE_B": {{
    "tracks": [
      {{"track_number": 1, "title": "...", "start_time_sec": 0, "end_time_sec": 456}}
    ]
  }}
}}

Pokud u poslední skladby není čas konce, nastav "end_time_sec": null.

Text tracklistu:
---
{pdf_text}
---
"""

        payload = {
            "model": self.MODEL_NAME,
            "messages": [{"role": "user", "content": prompt}],
            "response_format": {"type": "json_object"},
            "temperature": 0.0,
        }

        self._log_status("Odesílám požadavek na AI...")
        resp = requests.post(self.API_URL, headers=self.headers, json=payload, timeout=90)
        resp.raise_for_status()

        raw = json.loads(resp.json()["choices"][0]["message"]["content"])

        # --- převod na MM:SS ---
        for side_key in ("SIDE_A", "SIDE_B"):
            tracks = raw.get(side_key, {}).get("tracks", [])
            total_sec = 0
            shortest = None
            for t in tracks:
                start_sec = t.pop("start_time_sec", 0)
                end_sec   = t.pop("end_time_sec", None)

                t["start_mmss"] = sec_to_mmss(start_sec)
                t["end_mmss"]   = sec_to_mmss(end_sec)

                dur_sec = (end_sec - start_sec) if isinstance(end_sec, (int, float)) else None
                t["duration_mmss"] = sec_to_mmss(dur_sec)

                if dur_sec:
                    total_sec += dur_sec
                    if shortest is None or dur_sec < shortest:
                        shortest = dur_sec

            raw[side_key]["total_duration_mmss"]          = sec_to_mmss(total_sec)
            raw[side_key]["shortest_track_duration_mmss"] = sec_to_mmss(shortest)
            raw[side_key]["track_count"]                  = len(tracks)

        return raw


# --- ARCHIVE & AUTO-MATCHING ---
class ArchiveProcessor:
    def __init__(self, update_status_callback=None):
        self.update_status = update_status_callback

    def _log_status(self, msg):
        if self.update_status:
            self.update_status(msg)

    def _list_archive_files(self, archive_path: Path):
        if archive_path.suffix.lower() == ".zip":
            with zipfile.ZipFile(archive_path) as zf:
                return [Path(zf.namelist()[i]) for i in range(len(zf.namelist()))]
        elif archive_path.suffix.lower() in {".rar", ".cbr"}:
            if not RAR_AVAILABLE:
                raise RuntimeError("RAR není podporován – chybí knihovna `rarfile`.")
            with rarfile.RarFile(archive_path) as rf:
                return [Path(rf.namelist()[i]) for i in range(len(rf.namelist()))]
        else:
            raise ValueError("Nepodporovaný formát archivu.")

    def _extract_to(self, archive_path: Path, temp_dir: Path):
        self._log_status("Rozbaluji archiv...")
        if archive_path.suffix.lower() == ".zip":
            with zipfile.ZipFile(archive_path) as zf:
                zf.extractall(temp_dir)
                return [temp_dir / name for name in zf.namelist()]
        else:
            with rarfile.RarFile(archive_path) as rf:
                rf.extractall(temp_dir)
                return [temp_dir / name for name in rf.namelist()]

    def _match_wavs(self, wav_files):
        side_a = side_b = None
        for wav in wav_files:
            name = wav.name.upper()
            if ("A" in name and "B" not in name) or "SIDE_A" in name or name.endswith("_A.WAV"):
                side_a = wav
            elif ("B" in name and "A" not in name) or "SIDE_B" in name or name.endswith("_B.WAV"):
                side_b = wav
        if not side_a or not side_b:
            if len(wav_files) >= 2:
                side_a, side_b = wav_files[0], wav_files[1]
            else:
                raise ValueError("V archivu nejsou 2 WAV soubory.")
        return side_a, side_b

    def process_archive(self, archive_path: Path) -> dict:
        files = self._list_archive_files(archive_path)
        pdf_files = [f for f in files if f.suffix.lower() == ".pdf"]
        wav_files = [f for f in files if f.suffix.lower() == ".wav"]

        if len(pdf_files) != 1:
            raise ValueError("V archivu musí být právě jeden PDF soubor.")
        if len(wav_files) < 2:
            raise ValueError("V archivu musí být minimálně dva WAV soubory.")

        with TemporaryDirectory() as tmp:
            tmp_path = Path(tmp)
            extracted = self._extract_to(archive_path, tmp_path)

            pdf_path = next(tmp_path / f for f in extracted if f.name == pdf_files[0].name)
            wav_paths = [tmp_path / f for f in extracted if f.suffix.lower() == ".wav"]

            wav_a, wav_b = self._match_wavs(wav_paths)

            ai = TracklistProcessorAI(os.getenv("OPENROUTER_API_KEY"), self.update_status)
            data = ai.extract_tracklist_data(pdf_path)

            data["SIDE_A"]["wav_filename"] = wav_a.name
            data["SIDE_B"]["wav_filename"] = wav_b.name

            return data


# --- GUI (beze změn oproti předchozí plné verzi) ---
class App:
    def __init__(self, root, api_key):
        self.root = root
        self.api_key = api_key
        self.archive_path = tk.StringVar()

        self.root.title("Vinyl Mastering Validator 2.0 – MM:SS")
        self.root.geometry("750x650")
        self.root.minsize(600, 500)

        style = ttk.Style()
        style.theme_use("clam")
        style.configure("TButton", padding=6, font=("Segoe UI", 10))
        style.configure("Header.TLabel", font=("Segoe UI", 12, "bold"))

        main = ttk.Frame(root, padding=15)
        main.pack(fill=tk.BOTH, expand=True)

        ttk.Label(main, text="1. Vyberte ZIP/RAR archiv", style="Header.TLabel").pack(anchor="w")
        browse_btn = ttk.Button(main, text="Procházet...", command=self.select_archive)
        browse_btn.pack(pady=5, anchor="w")
        self.file_lbl = ttk.Label(main, textvariable=self.archive_path, wraplength=600)
        self.file_lbl.pack(anchor="w", pady=(0, 15))

        ttk.Label(main, text="2. Spusťte analýzu", style="Header.TLabel").pack(anchor="w")
        self.run_btn = ttk.Button(main, text="Zpracovat archiv", command=self.start)
        self.run_btn.pack(pady=5, anchor="w")

        result_frame = ttk.Frame(main)
        result_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        self.result_txt = tk.Text(result_frame, wrap=tk.WORD, font=("Consolas", 10))
        self.result_txt.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scroll = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_txt.yview)
        scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.result_txt.config(yscrollcommand=scroll.set)

        self.status_var = tk.StringVar(value="Připraveno.")
        ttk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN, anchor="w", padding=5).pack(fill=tk.X, side=tk.BOTTOM)

    def select_archive(self):
        path = filedialog.askopenfilename(
            title="Vyberte ZIP nebo RAR archiv",
            filetypes=(("ZIP/RAR", "*.zip *.rar"), ("Všechny soubory", "*.*"))
        )
        if path:
            self.archive_path.set(path)
            self.status_var.set(f"Vybrán: {Path(path).name}")

    def start(self):
        path = Path(self.archive_path.get())
        if not path.exists():
            messagebox.showerror("Chyba", "Nejprve vyberte platný archiv.")
            return

        self.run_btn.config(state=tk.DISABLED)
        self.result_txt.delete(1.0, tk.END)
        self.status_var.set("Zahajuji...")

        thread = threading.Thread(target=self._thread_worker, args=(path,), daemon=True)
        thread.start()

    def _thread_worker(self, path):
        try:
            processor = ArchiveProcessor(
                update_status_callback=lambda m: self.root.after(0, self.status_var.set, m)
            )
            data = processor.process_archive(path)
            self.root.after(0, self._show_success, data)
        except Exception as e:
            self.root.after(0, self._show_error, str(e))

    def _show_success(self, data):
        self.result_txt.insert(tk.END, json.dumps(data, indent=2, ensure_ascii=False))
        self.run_btn.config(state=tk.NORMAL)

    def _show_error(self, err):
        self.result_txt.config(fg="red")
        self.result_txt.insert(tk.END, f"Chyba:\n\n{err}")
        self.result_txt.config(fg="black")
        self.status_var.set("Chyba při zpracování.")
        self.run_btn.config(state=tk.NORMAL)


# --- MAIN ---
def main():
    load_dotenv()
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key:
        messagebox.showerror(
            "Chyba konfigurace",
            "V .env souboru chybí OPENROUTER_API_KEY."
        )
        sys.exit(1)

    root = tk.Tk()
    App(root, api_key)
    root.mainloop()


if __name__ == "__main__":
    main()