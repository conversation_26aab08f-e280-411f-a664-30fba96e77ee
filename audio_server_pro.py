# ============================================================
# audio_server_pro_compact.py - Vinyl Track Detector PRO v2.1
# ============================================================

import os
import io
import json
import time
import asyncio
import logging
import tempfile
import base64
from typing import Dict, Any, List, Tuple
from contextlib import asynccontextmanager
from pathlib import Path

import numpy as np
import soundfile as sf
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

from mcp.server.fastmcp import FastMCP, Context

# ------------------------------------------------------------
# Konfigurace
# ------------------------------------------------------------
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("vinyl-track-detector-pro")

mcp = FastMCP(
    "vinyl-track-detector-pro",
    description="Professional vinyl track detection with GUI-grade accuracy and compact visualization",
    version="2.1.0"
)

# ------------------------------------------------------------
# Detekce skladeb (identická s GUI)
# ------------------------------------------------------------
class TrackDetectorPro:
    """Identická logika jako GUI aplikace"""
    
    @staticmethod
    def detect_tracks(data: np.ndarray, samplerate: int) -> List[Tuple[float, float]]:
        """Kopie algoritmu z GUI aplikace"""
        
        mono_data = np.mean(data, axis=1) if data.ndim > 1 else data
        max_val = np.max(np.abs(mono_data))
        if max_val > 0:
            mono_data = mono_data / max_val

        # Parametry z GUI
        silence_threshold = 0.02
        min_silence_duration = 1.0
        min_silence_samples = int(samplerate * min_silence_duration)

        # Vektorizovaná detekce
        is_silent = np.abs(mono_data) < silence_threshold
        
        state_changes = np.diff(is_silent.astype(np.int8))
        silence_starts = np.where(state_changes == 1)[0] + 1
        silence_ends = np.where(state_changes == -1)[0] + 1

        # Korekce hranic
        if len(silence_starts) == 0 or len(silence_ends) == 0:
            boundaries_samples = [(0, len(mono_data))]
        else:
            if silence_ends[0] < silence_starts[0]:
                silence_starts = np.insert(silence_starts, 0, 0)
            if silence_starts[-1] > silence_ends[-1]:
                silence_ends = np.append(silence_ends, len(mono_data))

            boundaries = [0]
            for start, end in zip(silence_starts, silence_ends):
                if (end - start) >= min_silence_samples:
                    boundaries.append(start)  # Konec skladby
                    boundaries.append(end)    # Začátek další
            boundaries.append(len(mono_data))
            boundaries = sorted(list(set(boundaries)))

            # Filtr krátkých segmentů
            boundaries_samples = []
            for i in range(len(boundaries) - 1):
                start_sample, end_sample = boundaries[i], boundaries[i+1]
                duration = (end_sample - start_sample) / samplerate
                if duration > 0.5 and not np.all(is_silent[start_sample:end_sample]):
                    boundaries_samples.append((start_sample / samplerate, end_sample / samplerate))

        return boundaries_samples

    @staticmethod
    def format_duration(seconds: float) -> str:
        minutes = int(seconds // 60)
        seconds_rem = seconds % 60
        return f"{minutes:02d}:{seconds_rem:06.3f}"

# ------------------------------------------------------------
# Vizualizace waveformy (kompaktní)
# ------------------------------------------------------------
class WaveformVisualizer:
    """Generuje kompaktní PNG pro lokální zobrazení"""
    
    @staticmethod
    def save_png(data: np.ndarray, samplerate: int, boundaries: List[Tuple[float, float]], output_path: Path) -> None:
        """Uloží PNG waveformu do souboru"""
        
        mono_data = np.mean(data, axis=1) if data.ndim > 1 else data
        
        # Kompaktní downsampling
        max_points = 5000
        step = max(1, len(mono_data) // max_points)
        plot_data = mono_data[::step]
        time_axis = np.linspace(0, len(mono_data) / samplerate, len(plot_data))
        
        fig, ax = plt.subplots(figsize=(10, 2.5), dpi=100, facecolor='#f0f0f0')
        ax.plot(time_axis, plot_data, color='#007acc', linewidth=0.8)
        
        # Hranice skladeb
        for start, end in boundaries:
            ax.axvline(x=start, color='red', linestyle='--', linewidth=1, alpha=0.8)
        
        ax.set_title("Waveforma s detekovanými hranicemi", fontsize=10)
        ax.set_xlabel("Čas [s]", fontsize=9)
        ax.set_ylabel("Amplituda", fontsize=9)
        ax.grid(True, linestyle=':', alpha=0.4)
        ax.margins(x=0.01)
        plt.tight_layout()
        
        plt.savefig(output_path, dpi=100, bbox_inches='tight')
        plt.close(fig)

# ------------------------------------------------------------
# Životní cyklus serveru
# ------------------------------------------------------------
@asynccontextmanager
async def vinyl_lifespan(server: FastMCP):
    logger.info("⚡ Vinyl Track Detector PRO starting...")
    stats = type('Stats', (), {
        'analysis_count': 0,
        'total_tracks_detected': 0,
        'total_processing_time': 0.0
    })()
    
    try:
        yield stats
    finally:
        logger.info(f"📊 Celkové statistiky: {stats.analysis_count} analýz")

# ------------------------------------------------------------
# Hlavní MCP nástroj
# ------------------------------------------------------------
@mcp.tool()
async def analyze_vinyl_tracks(
    file_path: str,
    ctx: Context
) -> Dict[str, Any]:
    """Analyzuje vinylový master s GUI-přesnou detekcí"""
    
    start_time = time.time()
    
    # Přístup ke statistikám
    stats = ctx.request_context.lifespan_context
    
    result = {
        "status": "success",
        "file": os.path.basename(file_path),
        "track_count": 0,
        "tracks": [],
        "waveform_url": "",
        "waveform_instructions": "",
        "processing_time": 0.0,
        "samplerate": None,
        "total_duration": 0.0
    }
    
    try:
        # Validace
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Soubor nebyl nalezen: {file_path}")
        
        if not file_path.lower().endswith('.wav'):
            raise ValueError("Podporovány jsou pouze WAV soubory")
        
        # Načtení audio
        await ctx.info(f"🔍 Analýza: {os.path.basename(file_path)}")
        data, samplerate = sf.read(file_path, always_2d=True)
        total_duration = len(data) / samplerate
        
        # Detekce
        boundaries = TrackDetectorPro.detect_tracks(data, samplerate)
        
        # Příprava výsledků
        tracks = []
        for start, end in boundaries:
            duration = end - start
            tracks.append({
                "start_seconds": round(start, 3),
                "end_seconds": round(end, 3),
                "duration_seconds": round(duration, 3),
                "duration_formatted": TrackDetectorPro.format_duration(duration)
            })
        
        # Uložení PNG waveformy
        temp_dir = Path(tempfile.gettempdir()) / "vinyl_waveforms"
        temp_dir.mkdir(exist_ok=True)
        
        safe_name = Path(file_path).stem.replace(' ', '_').replace('/', '_')
        png_path = temp_dir / f"{safe_name}_waveform.png"
        
        WaveformVisualizer.save_png(data, samplerate, boundaries, png_path)
        
        # Lokální URL
        local_url = f"file:///{png_path}".replace('\\', '/')
        
        result.update({
            "track_count": len(tracks),
            "tracks": tracks,
            "waveform_url": local_url,
            "waveform_instructions": f"Pro zobrazení waveformy otevřete:\n{local_url}",
            "samplerate": samplerate,
            "total_duration": total_duration
        })
        
        # Statistiky
        stats.analysis_count += 1
        stats.total_tracks_detected += len(tracks)
        
        await ctx.info(f"✅ Detekováno {len(tracks)} skladeb, PNG uloženo do: {png_path}")
        
    except Exception as e:
        await ctx.error(f"❌ Chyba: {str(e)}")
        result["status"] = "error"
        result["message"] = str(e)
    
    finally:
        result["processing_time"] = round(time.time() - start_time, 2)
    
    return result

# ------------------------------------------------------------
# Spuštění
# ------------------------------------------------------------
mcp.lifespan = vinyl_lifespan

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Vinyl Track Detector PRO v2.1")
    parser.add_argument("--port", type=int, default=8080)
    parser.add_argument("--host", type=str, default="127.0.0.1")
    parser.add_argument("--transport", choices=["stdio", "http"], default="stdio")
    
    args = parser.parse_args()
    
    if args.transport == "http":
        mcp.run(transport="sse", host=args.host, port=args.port)
    else:
        mcp.run(transport="stdio")